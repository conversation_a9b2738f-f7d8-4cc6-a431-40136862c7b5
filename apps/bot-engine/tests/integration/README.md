# Bot Engine Integration Tests

This directory contains comprehensive integration tests for the bot-engine setup to verify that all components are working correctly in a local development environment.

## Test Structure

```
tests/integration/
├── README.md                           # This file
├── run-integration-tests.js            # Main test runner
├── config/
│   ├── test-config.js                  # Test configuration
│   └── environment.js                  # Environment setup
├── gemini-cli/
│   ├── install-test.js                 # Gemini CLI installation test
│   ├── functionality-test.js           # Gemini CLI functionality test
│   └── integration-test.js             # Gemini CLI integration with bot-engine
├── endpoints/
│   ├── health-check-test.js            # Health endpoint tests
│   ├── webhook-test.js                 # Webhook endpoint tests
│   └── message-processing-test.js      # Message processing tests
├── repository/
│   ├── fetch-test.js                   # Repository fetching tests
│   ├── worktree-test.js                # Git worktree creation tests
│   ├── gemini-integration-test.js      # Gemini CLI working on repositories
│   └── pull-request-test.js            # Pull request submission tests
├── scaffolding/
│   ├── new-repo-test.js                # New repository scaffolding tests
│   ├── github-push-test.js             # GitHub push tests
│   └── deployment-test.js              # Deployment integration tests
└── utils/
    ├── test-helpers.js                 # Common test utilities
    ├── mock-data.js                    # Test data and fixtures
    └── cleanup.js                      # Test cleanup utilities
```

## Prerequisites

Before running these tests, ensure you have:

1. **Environment Variables**: Set up the required environment variables in `.env.local`:
   ```bash
   GITHUB_TOKEN=your_github_token
   GOOGLE_API_KEY=your_google_api_key
   TWILIO_ACCOUNT_SID=your_twilio_sid
   TWILIO_AUTH_TOKEN=your_twilio_token
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_key
   VERCEL_TOKEN=your_vercel_token
   ```

2. **Node.js and pnpm**: Ensure you have Node.js 18+ and pnpm installed

3. **Git Configuration**: Ensure git is configured with user.name and user.email

4. **Network Access**: Tests require internet access for API calls and package installation

## Running Tests

### Run All Tests
```bash
# From the bot-engine directory
pnpm test:integration

# Or directly
node tests/integration/run-integration-tests.js
```

### Run Specific Test Categories
```bash
# Test only Gemini CLI
node tests/integration/run-integration-tests.js --category=gemini-cli

# Test only endpoints
node tests/integration/run-integration-tests.js --category=endpoints

# Test only repository operations
node tests/integration/run-integration-tests.js --category=repository

# Test only scaffolding
node tests/integration/run-integration-tests.js --category=scaffolding
```

### Run Individual Tests
```bash
# Test Gemini CLI installation
node tests/integration/gemini-cli/install-test.js

# Test webhook endpoints
node tests/integration/endpoints/webhook-test.js
```

## Test Categories

### 1. Gemini CLI Tests (`gemini-cli/`)
- **Installation Test**: Checks if gemini-cli is installed, installs it if missing
- **Functionality Test**: Verifies gemini-cli can process basic commands
- **Integration Test**: Tests gemini-cli integration with bot-engine components

### 2. Endpoint Tests (`endpoints/`)
- **Health Check Test**: Verifies bot-engine health endpoints are responding
- **Webhook Test**: Tests webhook endpoint functionality
- **Message Processing Test**: Tests end-to-end message processing

### 3. Repository Tests (`repository/`)
- **Fetch Test**: Tests repository cloning and fetching
- **Worktree Test**: Tests git worktree creation and management
- **Gemini Integration Test**: Tests gemini-cli working on repository code
- **Pull Request Test**: Tests PR creation and submission

### 4. Scaffolding Tests (`scaffolding/`)
- **New Repository Test**: Tests new repository creation and scaffolding
- **GitHub Push Test**: Tests pushing new repositories to GitHub
- **Deployment Test**: Tests deployment integration

## Test Output

Tests provide detailed output including:
- ✅ Success indicators for passing tests
- ❌ Error indicators with detailed error messages
- ⏱️ Timing information for performance monitoring
- 📊 Summary statistics at the end

## Cleanup

Tests automatically clean up after themselves, but you can manually run cleanup:

```bash
node tests/integration/utils/cleanup.js
```

This will remove:
- Test repositories created during testing
- Temporary workspaces
- Test artifacts

## Troubleshooting

### Common Issues

1. **Gemini CLI Not Found**: Ensure npm global bin directory is in PATH
2. **Permission Errors**: Check file permissions for workspace directories
3. **API Rate Limits**: Tests may fail if API rate limits are exceeded
4. **Network Issues**: Ensure stable internet connection for API calls

### Debug Mode

Run tests with debug output:
```bash
DEBUG=1 node tests/integration/run-integration-tests.js
```

### Verbose Mode

Run tests with verbose output:
```bash
VERBOSE=1 node tests/integration/run-integration-tests.js
```

## Contributing

When adding new integration tests:

1. Follow the existing directory structure
2. Use the test helpers in `utils/test-helpers.js`
3. Ensure proper cleanup in test teardown
4. Add appropriate error handling and logging
5. Update this README with new test descriptions
